package com.ebon.energy.fms.service.factory;

import com.ebon.energy.fms.common.enums.SystemStatusEnum.BatteryStatusValue;
import com.ebon.energy.fms.common.enums.SystemStatusEnum.GridStatusValue;
import com.ebon.energy.fms.common.enums.SystemStatusEnum.Status;
import com.ebon.energy.fms.common.utils.SiteOverviewHelper;
import com.ebon.energy.fms.domain.entity.DailyTotalVO;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.site.AllAboutDevice;
import com.ebon.energy.fms.domain.vo.site.AllAboutSiteOverview;
import com.ebon.energy.fms.domain.vo.site.BatteryStatusDto;
import com.ebon.energy.fms.domain.vo.site.EnergyFlowDto;
import com.ebon.energy.fms.domain.vo.telemetry.BatteryStatus;
import io.vavr.Tuple2;
import io.vavr.Tuple3;
import io.vavr.Tuple4;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

public class MultiSiteAllDifferentPhases implements ISiteConfigurationAggregator {

    @Override
    public AllAboutSiteOverview processSiteOverview(String publicSiteId, List<AllAboutDevice> siteDevices) {
        if (CollectionUtils.isEmpty(siteDevices)) {
            return null;
        }

        try {
            // 基础属性提取
            boolean hasSupportForConnectedPV = siteDevices.stream()
                    .anyMatch(d -> Objects.nonNull(d.getSpecification()) && d.getSpecification().isSupportsConnectedPV());

            boolean hasSolar = siteDevices.stream().anyMatch(AllAboutDevice::getHasSolar);
            boolean hasBatteries = siteDevices.stream().anyMatch(AllAboutDevice::getHasBatteries);

            LocalDate supportsLoadContributorsSince = siteDevices.stream()
                    .map(AllAboutDevice::getSupportsLoadContributorsSince)
                    .filter(Objects::nonNull)
                    .min(LocalDate::compareTo)
                    .orElse(null);

            WattHour maximumPossiblePVOutput = siteDevices.stream()
                    .map(AllAboutDevice::getMaximumPossiblePVOutput)
                    .filter(Objects::nonNull)
                    .reduce(WattHour.Zero, WattHour::add); // 假设WattHour支持加法

            boolean batteryMismatchProtectionEnabled = siteDevices.stream()
                    .anyMatch(d -> d.getHasBatteries() && d.getIsBatteryMismatchProtectionEnabled());

            ZonedDateTime lastStatusDateUtc = siteDevices.stream()
                    .map(d -> d.getDataForDashboard())
                    .filter(Objects::nonNull)
                    .map(DataForDashboardVO::getLastStatusDateUtc)
                    .min(ZonedDateTime::compareTo)
                    .orElse(null);

            boolean measuringThirdPartyInverter = siteDevices.stream()
                    .anyMatch(d -> Objects.nonNull(d.getSpecification()) && d.getSpecification().isMeasuringThirdPartyInverter());

            boolean isAcCoupledMode = siteDevices.stream()
                    .anyMatch(d -> Objects.nonNull(d.getSpecification()) && d.getSpecification().isInAcCoupledMode());

            // 处理DataForDashboard为null的设备
            if (siteDevices.stream().anyMatch(d -> d.getDataForDashboard() == null)) {
                DataForDashboardVO emptySiteData = new DataForDashboardVO(
                        null,
                        "5.0.0",
                        lastStatusDateUtc != null ? lastStatusDateUtc : ZonedDateTime.now(),
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        false,
                        null,
                        null
                );

                return new AllAboutSiteOverview(
                        publicSiteId,
                        emptySiteData,
                        siteDevices,
                        siteDevices.get(0).getTodaysDateInLocalTime(), // 假设列表非空
                        hasSupportForConnectedPV,
                        maximumPossiblePVOutput,
                        hasSolar,
                        hasBatteries,
                        batteryMismatchProtectionEnabled,
                        supportsLoadContributorsSince,
                        measuringThirdPartyInverter,
                        isAcCoupledMode
                );
            }

            // 组合电池状态
            List<BatteryStatus> batterySummaries = siteDevices.stream()
                    .filter(AllAboutDevice::getHasBatteries)
                    .map(AllAboutDevice::getDataForDashboard)
                    .map(DataForDashboardVO::getBatterySummary)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            BatteryStatus batteryStatus;
            boolean bmsVersionMismatch;
            if (!batterySummaries.isEmpty()) {
                Tuple2<BatteryStatus, Boolean> result = SiteOverviewHelper.combineBatteryStatuses(batterySummaries);
                batteryStatus = result._1;
                bmsVersionMismatch = result._2;
            } else {
                batteryStatus = new BatteryStatus();
                bmsVersionMismatch = false;
            }

            // 组合其他状态（假设SiteOverviewHelper方法返回对应类型）
            Tuple2<Watt, GridStatusValue> grid = SiteOverviewHelper.combineGridStatusesOnDifferentPhases(
                    siteDevices.stream().map(AllAboutDevice::getDataForDashboard).collect(Collectors.toList()));

            Tuple4<String, String, String, Watt> inverterStatus = SiteOverviewHelper.combineInverterStatuses(
                    siteDevices.stream().map(AllAboutDevice::getDataForDashboard).collect(Collectors.toList()));

            Tuple2<Watt, BigDecimal> thirdPartyInverterStatus = SiteOverviewHelper.combineThirdPartyInverterStatuses(
                    siteDevices.stream().map(AllAboutDevice::getDataForDashboard).collect(Collectors.toList()));

            Tuple2<Watt, BigDecimal> pvStatus = SiteOverviewHelper.combinePVStatuses(
                    siteDevices.stream().map(AllAboutDevice::getDataForDashboard).collect(Collectors.toList()));

            DailyEnergyTotalsVO dayTotalsE = SiteOverviewHelper.combineDayTotalsOnDifferentPhases(
                    siteDevices.stream().map(AllAboutDevice::getDataForDashboard).collect(Collectors.toList()));

            Tuple2<Watt, Status> backupLoadStatus = SiteOverviewHelper.combineBackupLoadStatuses(
                    siteDevices.stream().map(AllAboutDevice::getDataForDashboard).collect(Collectors.toList()));

            Tuple3<ZonedDateTime, String, Boolean> ouijaBoard = SiteOverviewHelper.combineOuijaBoardDetails(
                    siteDevices.stream().map(AllAboutDevice::getDataForDashboard).collect(Collectors.toList()));

            Tuple2<Watt, BigDecimal> acLoadStatus = SiteOverviewHelper.combineACLoadStatuses(
                    inverterStatus._4,
                    thirdPartyInverterStatus._1,
                    grid._1,
                    backupLoadStatus._1
            );

            // 计算站点出口限制
            Integer siteExportLimitW = siteDevices.stream()
                    .map(d -> d.getDataForDashboard().getSiteExportLimitW())
                    .filter(Objects::nonNull)
                    .max(Integer::compareTo)
                    .orElse(null);

            // 处理离线状态
            boolean siteOffLine = SiteOverviewHelper.isOffline(lastStatusDateUtc);
            if (siteOffLine || batteryStatus.getStatus() == BatteryStatusValue.Disabled || batteryStatus.getStatus() == BatteryStatusValue.Disconnected) {
                siteExportLimitW = null;
                pvStatus = new Tuple2<>(Watt.Zero, null);
                thirdPartyInverterStatus = new Tuple2<>(Watt.Zero, null);
                acLoadStatus = new Tuple2<>(Watt.Zero, null);
                backupLoadStatus = new Tuple2<>(Watt.Zero, Status.Off);

                batteryStatus = new BatteryStatus();
                batteryStatus.setStatus(siteOffLine ? BatteryStatusValue.Idle : batteryStatus.getStatus());
                batteryStatus.setP(0.0);
                batteryStatus.setI(0.0);
                batteryStatus.setSoC(100.0);

                inverterStatus = new Tuple4<>("", "", "", Watt.Zero);
                grid = new Tuple2<>(Watt.Zero, GridStatusValue.Idle);
                dayTotalsE = null;
            }

            DataForDashboardVO siteData = new DataForDashboardVO(
                    siteExportLimitW,
                    "5.0.0",
                    lastStatusDateUtc,
                    acLoadStatus._1,
                    backupLoadStatus._2,
                    backupLoadStatus._1,
                    grid._2,
                    grid._1,
                    inverterStatus._1,
                    inverterStatus._2,
                    inverterStatus._3,
                    inverterStatus._4,
                    thirdPartyInverterStatus._1,
                    pvStatus._1,
                    batteryStatus,
                    ouijaBoard._1,
                    ouijaBoard._2,
                    ouijaBoard._3,
                    bmsVersionMismatch,
                    dayTotalsE,
                    null
            );

            return new AllAboutSiteOverview(
                    publicSiteId,
                    siteData,
                    siteDevices,
                    siteDevices.get(0).getTodaysDateInLocalTime(), // 假设列表非空
                    hasSupportForConnectedPV,
                    maximumPossiblePVOutput,
                    hasSolar,
                    hasBatteries,
                    batteryMismatchProtectionEnabled,
                    supportsLoadContributorsSince,
                    measuringThirdPartyInverter,
                    isAcCoupledMode
            );
        } catch (Exception ex) {
            return null;
        }
    }

    @Override
    public List<DailyTotalVO> processSiteNDayHistory(String publicSiteId, List<Tuple2<AllAboutDevice, List<DailyTotalVO>>> deviceHistories, int twiceDays) {
        // 按日期分组所有设备的总计数据
        Map<Timestamp, List<DailyTotalVO>> dailyTotals = new HashMap<>();
        if (deviceHistories != null) {
            for (Tuple2<AllAboutDevice, List<DailyTotalVO>> dailyHistory : deviceHistories) {
                if (dailyHistory._2 != null) {
                    for (DailyTotalVO dailyTotalVO : dailyHistory._2) {
                        if (dailyTotalVO != null && dailyTotalVO.getDateDate() != null) {
                            if (!dailyTotals.containsKey(dailyTotalVO.getDateDate())) {
                                dailyTotals.put(dailyTotalVO.getDateDate(), new ArrayList<>());
                            }

                            dailyTotals.get(dailyTotalVO.getDateDate()).add(dailyTotalVO);
                        }
                    }
                }
            }
        }

        // 按日期降序处理每一天的数据
        return dailyTotals.entrySet().stream()
                .sorted(Map.Entry.<Timestamp, List<DailyTotalVO>>comparingByKey().reversed())
                .map(entry -> {
                    List<DailyTotalVO> dayTotals = entry.getValue();
                    DailyTotalVO firstRecord = dayTotals.stream().findFirst().orElse(null);

                    // 计算每日总和
                    int dailyExportWh = sumOrZero(dayTotals, DailyTotalVO::getDailySoldWh);
                    boolean exportAdjusted = dayTotals.stream().anyMatch(DailyTotalVO::getDailySoldAdjusted);

                    int dailyImportWh = sumOrZero(dayTotals, DailyTotalVO::getDailyBoughtWh);
                    boolean importAdjusted = dayTotals.stream().anyMatch(DailyTotalVO::getDailyBoughtAdjusted);

                    int dailyGenerationWh = sumOrZero(dayTotals, DailyTotalVO::getDailyGenerationWh);
                    boolean generationAdjusted = dayTotals.stream().anyMatch(DailyTotalVO::getDailyGenerationAdjusted);

                    Integer dailyBatteryChargedWh = nullableSum(dayTotals, DailyTotalVO::getDailyBatteryChargedWh);
                    boolean batteryChargedAdjusted = dayTotals.stream()
                            .anyMatch(t -> t.getDailyBatteryChargedAdjusted() != null && t.getDailyBatteryChargedAdjusted());

                    Integer dailyBatteryDischargedWh = nullableSum(dayTotals, DailyTotalVO::getDailyBatteryDischargedWh);
                    boolean batteryDischargedAdjusted = dayTotals.stream()
                            .anyMatch(t -> t.getDailyBatteryDischargedAdjusted() != null && t.getDailyBatteryDischargedAdjusted());

                    // 计算每日使用量
                    int dailyBatteryDrainWh = (dailyBatteryDischargedWh != null ? dailyBatteryDischargedWh : 0) -
                            (dailyBatteryChargedWh != null ? dailyBatteryChargedWh : 0);
                    int dailyUsageWh = (dailyImportWh - dailyExportWh) + dailyGenerationWh + dailyBatteryDrainWh;

                    // 判断使用量是否调整
                    boolean usageAdjusted = importAdjusted || exportAdjusted || generationAdjusted ||
                            batteryChargedAdjusted || batteryDischargedAdjusted;

                    DailyTotalVO dailyTotalVO = new DailyTotalVO();
                    // 创建汇总DTO
                    dailyTotalVO.setDate(firstRecord != null ? firstRecord.getDate() : null);
                    dailyTotalVO.setDateDate(entry.getKey());
                    dailyTotalVO.setDailyUsageWh(dailyUsageWh);
                    dailyTotalVO.setDailySoldWh(dailyExportWh);
                    dailyTotalVO.setDailyBoughtWh(dailyImportWh);
                    dailyTotalVO.setDailyGenerationWh(dailyGenerationWh);
                    dailyTotalVO.setDailyBatteryChargedWh(dailyBatteryChargedWh);
                    dailyTotalVO.setDailyBatteryDischargedWh(dailyBatteryDischargedWh);
                    dailyTotalVO.setDailyUsageAdjusted(usageAdjusted);
                    dailyTotalVO.setDailySoldAdjusted(exportAdjusted);
                    dailyTotalVO.setDailyBoughtAdjusted(importAdjusted);
                    dailyTotalVO.setDailyGenerationAdjusted(generationAdjusted);
                    dailyTotalVO.setDailyBatteryChargedAdjusted(batteryChargedAdjusted);
                    dailyTotalVO.setDailyBatteryDischargedAdjusted(batteryDischargedAdjusted);
                    return dailyTotalVO;
                })
                .collect(Collectors.toList());
    }

    @Override
    public BatteryStatusDto ProcessSiteBatteryStatus(AllAboutSiteOverview allAboutSiteOverview, EnergyFlowDto siteFlow, int numberOfConsecutiveDaysBatteriesMismatchedToDisabled) {
        return null;
    }

    // 辅助方法：计算非空整数流的和
    private int sumOrZero(List<DailyTotalVO> list, java.util.function.ToIntFunction<DailyTotalVO> mapper) {
        return list.stream().mapToInt(mapper).sum();
    }

    // 辅助方法：计算可空整数流的和，若全部为空则返回null
    private Integer nullableSum(List<DailyTotalVO> list, java.util.function.Function<DailyTotalVO, Integer> mapper) {
        List<Integer> nonNullValues = list.stream()
                .map(mapper)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return nonNullValues.isEmpty() ? null : nonNullValues.stream().mapToInt(Integer::intValue).sum();
    }
}
