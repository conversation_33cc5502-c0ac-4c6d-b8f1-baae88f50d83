package com.ebon.energy.fms.util;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Optional;

/**
 * 日期时间工具类
 * 注意：新代码请使用 Redback.Utils.DateTimeHelper
 */
public final class DateTimeHelper {

    // 日期时间格式常量
    public static final String ISO_8601_URL_FRIENDLY_FORMAT = "yyyyMMdd'T'HHmmss'Z'";
    public static final String HUMAN_FRIENDLY_FORMAT = "yyyy-MM-dd HH:mm:ss'Z'";
    public static final String HUMAN_FRIENDLY_ZULU_TIME_FORMAT = "HH:mm:ssX";

    // 预定义的格式化器
    public static final DateTimeFormatter ISO_8601_URL_FRIENDLY_FORMATTER =
            DateTimeFormatter.ofPattern(ISO_8601_URL_FRIENDLY_FORMAT);
    public static final DateTimeFormatter HUMAN_FRIENDLY_FORMATTER =
            DateTimeFormatter.ofPattern(HUMAN_FRIENDLY_FORMAT);
    public static final DateTimeFormatter HUMAN_FRIENDLY_ZULU_TIME_FORMATTER =
            DateTimeFormatter.ofPattern(HUMAN_FRIENDLY_ZULU_TIME_FORMAT);

    private DateTimeHelper() {
        // 工具类，防止实例化
    }

    /**
     * 转换为从1970年1月1日开始的秒数（Unix时间戳）
     *
     * @param date 要转换的日期时间
     * @return 时间戳（秒）
     */
    public static long toEpoch(ZonedDateTime date) {
        return date.toEpochSecond();
    }

    /**
     * 去除毫秒部分
     *
     * @param dt 原始日期时间
     * @return 去除毫秒后的日期时间
     */
    public static ZonedDateTime withoutMillis(ZonedDateTime dt) {
        return dt == null ? null : dt.withZoneSameInstant(dt.getZone())
                .withNano(0);
    }

    /**
     * 解析时间字符串
     *
     * @param humanTimestamp 人类可读的时间字符串
     * @return 解析后的Instant
     * @throws IllegalArgumentException 如果解析失败
     */
    public static Instant parse(String humanTimestamp) {
        try {
            // 先尝试使用标准ISO格式解析
            return Instant.parse(humanTimestamp);
        } catch (DateTimeParseException e1) {
            try {
                // 尝试使用自定义格式解析
                LocalDateTime localDateTime = LocalDateTime.parse(
                        humanTimestamp,
                        DateTimeFormatter.ofPattern(HUMAN_FRIENDLY_FORMAT)
                );
                return localDateTime.atZone(ZoneOffset.UTC).toInstant();
            } catch (DateTimeParseException e2) {
                throw new IllegalArgumentException(
                        String.format("Failed to extract timestamp from '%s'", humanTimestamp),
                        e2
                );
            }
        }
    }

    /**
     * 安全解析时间字符串
     *
     * @param humanTimestamp 人类可读的时间字符串
     * @return 包含Instant的Optional，解析失败返回Optional.empty()
     */
    public static Optional<Instant> tryParse(String humanTimestamp) {
        try {
            return Optional.of(parse(humanTimestamp));
        } catch (IllegalArgumentException e) {
            return Optional.empty();
        }
    }


    
}